
<!DOCTYPE html>
<html>
<head>
    <title>SVG Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #0E0E10; color: white; }
        .project { margin: 20px 0; padding: 20px; border: 1px solid #3cc698; border-radius: 8px; }
        img { width: 200px; height: 150px; object-fit: cover; border: 1px solid #ccc; }
        .error { color: #ff6b6b; }
    </style>
</head>
<body>
    <h1>SVG Loading Test</h1>
    <p>This file tests if SVG images load correctly in the browser.</p>
    
    
    <div class="project">
        <h3>agoodmansview.svg</h3>
        <img src="./projects/agoodmansview.svg" alt="agoodmansview.svg" onerror="this.nextElementSibling.style.display='block'">
        <div class="error" style="display:none">❌ Failed to load agoodmansview.svg</div>
    </div>
    
    <div class="project">
        <h3>fintrack.svg</h3>
        <img src="./projects/fintrack.svg" alt="fintrack.svg" onerror="this.nextElementSibling.style.display='block'">
        <div class="error" style="display:none">❌ Failed to load fintrack.svg</div>
    </div>
    
    <div class="project">
        <h3>lms.svg</h3>
        <img src="./projects/lms.svg" alt="lms.svg" onerror="this.nextElementSibling.style.display='block'">
        <div class="error" style="display:none">❌ Failed to load lms.svg</div>
    </div>
    
    <div class="project">
        <h3>portfolio.svg</h3>
        <img src="./projects/portfolio.svg" alt="portfolio.svg" onerror="this.nextElementSibling.style.display='block'">
        <div class="error" style="display:none">❌ Failed to load portfolio.svg</div>
    </div>
    
    
    <script>
        console.log('Testing SVG file loading...');
        document.querySelectorAll('img').forEach((img, index) => {
            img.onload = () => console.log(`✅ Loaded: ${img.src}`);
            img.onerror = () => console.log(`❌ Failed: ${img.src}`);
        });
    </script>
</body>
</html>
