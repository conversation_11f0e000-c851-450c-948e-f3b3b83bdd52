<svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0E0E10;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#191F3A;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0E0E10;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="bookGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3cc698;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00FFFF;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="300" fill="url(#bg)"/>
  
  <!-- Library shelves -->
  <rect x="50" y="80" width="300" height="8" fill="#3cc698" opacity="0.6"/>
  <rect x="50" y="140" width="300" height="8" fill="#3cc698" opacity="0.6"/>
  <rect x="50" y="200" width="300" height="8" fill="#3cc698" opacity="0.6"/>
  
  <!-- Books on shelves -->
  <!-- Top shelf -->
  <rect x="60" y="50" width="15" height="30" fill="#FF6B6B" opacity="0.8"/>
  <rect x="80" y="50" width="12" height="30" fill="#4ECDC4" opacity="0.8"/>
  <rect x="95" y="50" width="18" height="30" fill="#45B7D1" opacity="0.8"/>
  <rect x="118" y="50" width="14" height="30" fill="#96CEB4" opacity="0.8"/>
  <rect x="137" y="50" width="16" height="30" fill="#FFEAA7" opacity="0.8"/>
  <rect x="158" y="50" width="13" height="30" fill="#DDA0DD" opacity="0.8"/>
  <rect x="176" y="50" width="17" height="30" fill="#98D8C8" opacity="0.8"/>
  
  <!-- Middle shelf -->
  <rect x="60" y="110" width="14" height="30" fill="#F7DC6F" opacity="0.8"/>
  <rect x="79" y="110" width="16" height="30" fill="#BB8FCE" opacity="0.8"/>
  <rect x="100" y="110" width="13" height="30" fill="#85C1E9" opacity="0.8"/>
  <rect x="118" y="110" width="15" height="30" fill="#F8C471" opacity="0.8"/>
  <rect x="138" y="110" width="12" height="30" fill="#82E0AA" opacity="0.8"/>
  <rect x="155" y="110" width="18" height="30" fill="#F1948A" opacity="0.8"/>
  
  <!-- Bottom shelf -->
  <rect x="60" y="170" width="16" height="30" fill="#AED6F1" opacity="0.8"/>
  <rect x="81" y="170" width="14" height="30" fill="#A9DFBF" opacity="0.8"/>
  <rect x="100" y="170" width="17" height="30" fill="#F9E79F" opacity="0.8"/>
  <rect x="122" y="170" width="13" height="30" fill="#D7BDE2" opacity="0.8"/>
  <rect x="140" y="170" width="15" height="30" fill="#FADBD8" opacity="0.8"/>
  
  <!-- Computer/API representation -->
  <rect x="250" y="120" width="80" height="50" fill="#1E1E24" stroke="#3cc698" stroke-width="2" rx="4"/>
  <rect x="255" y="125" width="70" height="30" fill="#191F3A"/>
  
  <!-- API endpoints visualization -->
  <circle cx="265" cy="135" r="3" fill="#3cc698"/>
  <circle cx="275" cy="135" r="3" fill="#00FFFF"/>
  <circle cx="285" cy="135" r="3" fill="#3cc698"/>
  <circle cx="295" cy="135" r="3" fill="#00FFFF"/>
  
  <circle cx="265" cy="145" r="3" fill="#00FFFF"/>
  <circle cx="275" cy="145" r="3" fill="#3cc698"/>
  <circle cx="285" cy="145" r="3" fill="#00FFFF"/>
  <circle cx="295" cy="145" r="3" fill="#3cc698"/>
  
  <!-- Database representation -->
  <ellipse cx="320" cy="220" rx="25" ry="8" fill="#4169E1" opacity="0.8"/>
  <rect x="295" y="212" width="50" height="16" fill="#4169E1" opacity="0.8"/>
  <ellipse cx="320" cy="212" rx="25" ry="8" fill="#5179F1" opacity="0.9"/>
  
  <!-- Connection lines -->
  <line x1="290" y1="150" x2="320" y2="210" stroke="#3cc698" stroke-width="2" opacity="0.6"/>
  <line x1="250" y1="140" x2="200" y2="120" stroke="#3cc698" stroke-width="2" opacity="0.6"/>
  
  <!-- Title -->
  <text x="200" y="40" text-anchor="middle" fill="#B2BABB" font-family="Arial, sans-serif" font-size="18" font-weight="bold">Library Management System</text>
  <text x="200" y="280" text-anchor="middle" fill="#3cc698" font-family="Arial, sans-serif" font-size="12">Spring Boot • PostgreSQL • REST API</text>
</svg>
