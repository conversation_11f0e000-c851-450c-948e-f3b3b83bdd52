<svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0E0E10;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#191F3A;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0E0E10;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1E1E24;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#191F3A;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="moneyGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3cc698;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00FFFF;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="300" fill="url(#bg)"/>
  
  <!-- Mobile Banking Interface -->
  <rect x="80" y="60" width="120" height="180" fill="#2C2C2C" rx="12"/>
  <rect x="85" y="70" width="110" height="160" fill="url(#cardGradient)" rx="8"/>
  
  <!-- Phone screen content -->
  <!-- Header -->
  <rect x="90" y="75" width="100" height="15" fill="#3cc698" opacity="0.3"/>
  <text x="140" y="85" text-anchor="middle" fill="#3cc698" font-family="Arial, sans-serif" font-size="8" font-weight="bold">FinTrack</text>
  
  <!-- Balance display -->
  <rect x="90" y="95" width="100" height="25" fill="#191F3A" opacity="0.8"/>
  <text x="140" y="105" text-anchor="middle" fill="#B2BABB" font-family="Arial, sans-serif" font-size="6">Total Balance</text>
  <text x="140" y="115" text-anchor="middle" fill="#3cc698" font-family="Arial, sans-serif" font-size="10" font-weight="bold">R 25,430.50</text>
  
  <!-- Transaction list -->
  <rect x="90" y="125" width="100" height="12" fill="#1E1E24" stroke="#3cc698" stroke-width="0.5"/>
  <circle cx="95" cy="131" r="2" fill="#FF6B6B"/>
  <rect x="100" y="128" width="40" height="2" fill="#B2BABB"/>
  <rect x="100" y="132" width="25" height="1.5" fill="#B2BABB" opacity="0.7"/>
  <text x="185" y="132" text-anchor="end" fill="#FF6B6B" font-family="Arial, sans-serif" font-size="6">-R 150</text>
  
  <rect x="90" y="140" width="100" height="12" fill="#1E1E24" stroke="#00FFFF" stroke-width="0.5"/>
  <circle cx="95" cy="146" r="2" fill="#3cc698"/>
  <rect x="100" y="143" width="35" height="2" fill="#B2BABB"/>
  <rect x="100" y="147" width="30" height="1.5" fill="#B2BABB" opacity="0.7"/>
  <text x="185" y="147" text-anchor="end" fill="#3cc698" font-family="Arial, sans-serif" font-size="6">+R 2,500</text>
  
  <rect x="90" y="155" width="100" height="12" fill="#1E1E24" stroke="#3cc698" stroke-width="0.5"/>
  <circle cx="95" cy="161" r="2" fill="#FF6B6B"/>
  <rect x="100" y="158" width="45" height="2" fill="#B2BABB"/>
  <rect x="100" y="162" width="20" height="1.5" fill="#B2BABB" opacity="0.7"/>
  <text x="185" y="162" text-anchor="end" fill="#FF6B6B" font-family="Arial, sans-serif" font-size="6">-R 89.99</text>
  
  <!-- Bottom navigation -->
  <rect x="90" y="210" width="100" height="15" fill="#191F3A"/>
  <circle cx="105" cy="217" r="3" fill="#3cc698"/>
  <circle cx="125" cy="217" r="3" fill="#B2BABB"/>
  <circle cx="145" cy="217" r="3" fill="#B2BABB"/>
  <circle cx="165" cy="217" r="3" fill="#B2BABB"/>
  
  <!-- API/Backend representation -->
  <rect x="230" y="100" width="120" height="80" fill="#1E1E24" stroke="#3cc698" stroke-width="2" rx="6"/>
  <text x="290" y="115" text-anchor="middle" fill="#3cc698" font-family="Arial, sans-serif" font-size="10" font-weight="bold">Spring Boot API</text>
  
  <!-- API endpoints -->
  <rect x="240" y="125" width="100" height="8" fill="#191F3A"/>
  <text x="245" y="131" fill="#00FFFF" font-family="Arial, sans-serif" font-size="6">POST /api/v1/transactions</text>
  
  <rect x="240" y="135" width="100" height="8" fill="#191F3A"/>
  <text x="245" y="141" fill="#3cc698" font-family="Arial, sans-serif" font-size="6">GET /api/v1/accounts</text>
  
  <rect x="240" y="145" width="100" height="8" fill="#191F3A"/>
  <text x="245" y="151" fill="#00FFFF" font-family="Arial, sans-serif" font-size="6">GET /api/v1/budgets</text>
  
  <rect x="240" y="155" width="100" height="8" fill="#191F3A"/>
  <text x="245" y="161" fill="#3cc698" font-family="Arial, sans-serif" font-size="6">POST /api/v1/auth/login</text>
  
  <!-- JWT Token -->
  <rect x="240" y="165" width="100" height="10" fill="#3cc698" opacity="0.2"/>
  <text x="290" y="172" text-anchor="middle" fill="#3cc698" font-family="Arial, sans-serif" font-size="5">JWT Authentication</text>
  
  <!-- Database -->
  <ellipse cx="290" cy="220" rx="30" ry="10" fill="#4169E1" opacity="0.8"/>
  <rect x="260" y="210" width="60" height="20" fill="#4169E1" opacity="0.8"/>
  <ellipse cx="290" cy="210" rx="30" ry="10" fill="#5179F1" opacity="0.9"/>
  <text x="290" y="225" text-anchor="middle" fill="#B2BABB" font-family="Arial, sans-serif" font-size="8">PostgreSQL</text>
  
  <!-- AWS Cloud -->
  <rect x="50" y="20" width="80" height="25" fill="#FF9900" opacity="0.2" rx="4"/>
  <text x="90" y="35" text-anchor="middle" fill="#FF9900" font-family="Arial, sans-serif" font-size="10" font-weight="bold">AWS Cloud</text>
  
  <!-- AWS Services -->
  <rect x="55" y="45" width="20" height="8" fill="#FF9900" opacity="0.6"/>
  <text x="65" y="51" text-anchor="middle" fill="#B2BABB" font-family="Arial, sans-serif" font-size="5">RDS</text>
  
  <rect x="80" y="45" width="20" height="8" fill="#FF9900" opacity="0.6"/>
  <text x="90" y="51" text-anchor="middle" fill="#B2BABB" font-family="Arial, sans-serif" font-size="5">S3</text>
  
  <rect x="105" y="45" width="20" height="8" fill="#FF9900" opacity="0.6"/>
  <text x="115" y="51" text-anchor="middle" fill="#B2BABB" font-family="Arial, sans-serif" font-size="5">SES</text>
  
  <!-- Security/JWT indicators -->
  <circle cx="320" cy="70" r="8" fill="none" stroke="#3cc698" stroke-width="2"/>
  <text x="320" y="75" text-anchor="middle" fill="#3cc698" font-family="Arial, sans-serif" font-size="6">🔐</text>
  
  <!-- Money/Finance symbols -->
  <text x="60" y="180" fill="url(#moneyGradient)" font-family="Arial, sans-serif" font-size="20">R</text>
  <text x="350" y="60" fill="url(#moneyGradient)" font-family="Arial, sans-serif" font-size="16">$</text>
  <text x="370" y="180" fill="url(#moneyGradient)" font-family="Arial, sans-serif" font-size="18">€</text>
  
  <!-- Connection lines -->
  <line x1="200" y1="140" x2="230" y2="140" stroke="#3cc698" stroke-width="2" opacity="0.6"/>
  <line x1="290" y1="180" x2="290" y2="200" stroke="#4169E1" stroke-width="2" opacity="0.6"/>
  <line x1="130" y1="60" x2="90" y2="45" stroke="#FF9900" stroke-width="2" opacity="0.6"/>
  
  <!-- Animated elements -->
  <circle cx="210" cy="130" r="2" fill="#3cc698" opacity="0.8">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" repeatCount="indefinite"/>
  </circle>
  <circle cx="220" cy="150" r="1.5" fill="#00FFFF" opacity="0.6">
    <animate attributeName="opacity" values="0.2;0.8;0.2" dur="1.5s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Title -->
  <text x="200" y="25" text-anchor="middle" fill="#B2BABB" font-family="Arial, sans-serif" font-size="16" font-weight="bold">FinTrack API</text>
  <text x="200" y="285" text-anchor="middle" fill="#3cc698" font-family="Arial, sans-serif" font-size="11">Spring Boot • AWS • PostgreSQL • JWT</text>
</svg>
