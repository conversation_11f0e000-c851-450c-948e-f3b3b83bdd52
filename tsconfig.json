{"compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./*"]}, "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": false, "forceConsistentCasingInFileNames": true, "noEmit": true, "incremental": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "target": "ES2017", "plugins": [{"name": "next"}], "strictNullChecks": false, "noImplicitAny": false, "suppressImplicitAnyIndexErrors": true}, "include": ["**/*.ts", "**/*.tsx", "next-env.d.ts", ".next/types/**/*.ts", "types/**/*.d.ts"], "exclude": ["node_modules"]}