/* src/styles/global.css */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Performance optimizations */
@layer base {
  /* Optimize font rendering */
  html {
    font-display: swap;
    scroll-behavior: smooth;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  /* Optimize animations for performance */
  *, *::before, *::after {
    box-sizing: border-box;
  }

  /* Reduce motion for users who prefer it */
  @media (prefers-reduced-motion: reduce) {
    *, *::before, *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }
}

/* Custom global styles with CSS variables for better performance */
:root {
  --nhlaka-cyan: #00FFFF;
  --nhlaka-green: #C4FF00;
  --nhlaka-dark: #15142b;
  --nhlaka-gray: #4a4b5e;
  --nhlaka-light: #f4f4f6;

  /* Performance-optimized color palette */
  --primary-bg: var(--nhlaka-dark);
  --primary-text: var(--nhlaka-light);
  --accent-color: var(--nhlaka-cyan);
  --secondary-accent: var(--nhlaka-green);
}

body {
  font-family: 'Roboto', sans-serif;
  background-color: var(--primary-bg);
  color: var(--primary-text);
  margin: 0;
  padding: 0;
  line-height: 1.6;
  /* Optimize for better performance */
  will-change: auto;
}

a {
  text-decoration: none;
  color: inherit;
}

/* Animation classes */
.animate-glow-pulse {
  animation: glowPulse 3s ease-in-out infinite;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-scan-line {
  animation: scanLine 2s linear infinite;
}

.animate-energy-flow {
  animation: energyFlow 4s ease infinite;
}

.animate-fadeIn {
  animation: fadeIn 1s ease-in;
}

.animate-slideUp {
  animation: slideUp 0.7s ease-in-out;
}

/* Keyframes */
@keyframes glowPulse {
  0%, 100% { opacity: 0.6; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.05); }
}

@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

@keyframes scanLine {
  0% { transform: translateY(-100%); }
  100% { transform: translateY(100%); }
}

@keyframes energyFlow {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes slideUp {
  0% { transform: translateY(20px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

/* Custom utility classes */
@layer components {
  .nhlaka-gradient {
    @apply bg-gradient-to-r from-[#15142b] via-[#232244] to-[#2e2c56];
  }

  .nhlaka-accent {
    @apply text-[#00FFFF] hover:text-[#33FFFF] transition-colors;
  }

  .nhlaka-button {
    @apply bg-[#C4FF00] hover:bg-[#D0FF33] text-[#15142b] font-bold py-2 px-6 rounded-md transition-all;
  }

  .nhlaka-card {
    @apply bg-[#191F3A] border border-[#00FFFF]/20 rounded-lg shadow-lg p-6 transition-all duration-300 hover:shadow-[#00FFFF]/10 hover:border-[#00FFFF]/40;
  }

  .nhlaka-heading {
    @apply font-heading font-bold text-[#B2BABB] mb-4;
  }

  .nhlaka-subheading {
    @apply font-heading font-medium text-[#00FFFF] mb-2;
  }

  .nhlaka-text {
    @apply text-[#B2BABB] leading-relaxed;
  }

  .nhlaka-link {
    @apply text-[#00FFFF] hover:text-[#C4FF00] transition-colors duration-300 relative inline-block;
  }

  .nhlaka-link::after {
    @apply content-[''] absolute left-0 bottom-0 w-0 h-0.5 bg-[#C4FF00] transition-all duration-300;
  }

  .nhlaka-link:hover::after {
    @apply w-full;
  }

  .nhlaka-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
  }
}html {
  scroll-behavior: smooth;
}

/* Enhanced Cyan Glow Animations */
.cyan-glow {
  animation: cyanGlow 4s ease-in-out infinite;
  filter: blur(15px);
  opacity: 0.7 !important;
  box-shadow: 0 0 25px 12px rgba(0, 255, 255, 0.4);
}

.cyan-particle {
  background-color: #00FFFF !important;
  box-shadow: 0 0 15px 5px rgba(0, 255, 255, 0.6) !important;
  filter: blur(1px);
}

/* Enhanced keyframes for more visible animations */
@keyframes cyanGlow {
  0%, 100% { opacity: 0.5; transform: scale(1); box-shadow: 0 0 20px 8px rgba(0, 255, 255, 0.3); }
  50% { opacity: 0.9; transform: scale(1.1); box-shadow: 0 0 30px 15px rgba(0, 255, 255, 0.5); }
}

@keyframes cyanPulse {
  0%, 100% { opacity: 0.6; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.2); }
}

/* Add a visible animation class with glow */
.visible-animation {
  opacity: 0.7 !important;
  z-index: 1;
  filter: blur(1px);
  box-shadow: 0 0 15px 5px rgba(0, 255, 255, 0.5);
}
