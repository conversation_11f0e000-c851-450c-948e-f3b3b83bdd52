// src/components/SEO.tsx
import Head from 'next/head';
import { useRouter } from 'next/router';

interface SEOProps {
  title?: string;
  description?: string;
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'profile';
  keywords?: string[];
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  noindex?: boolean;
  canonical?: string;
}

const SEO: React.FC<SEOProps> = ({
  title = 'NHLAKANIPHO PORTFOLIO - JUNIOR SOFTWARE DEVELOPER',
  description = 'Professional portfolio of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, a driven junior software developer and Software Engineering student at WeThinkCode_ specializing in Python, Java, Flask, Spring Boot, React, MySQL, and PostgreSQL.',
  image = '/images/og-image.jpg',
  type = 'website',
  keywords = ['developer', 'portfolio', 'python', 'java', 'react', 'flask', 'spring boot', 'mysql', 'postgresql', 'software engineering', 'wethinkcode', 'hackathons', 'coding challenges'],
  author = 'Nhlakanipho',
  publishedTime,
  modifiedTime,
  noindex = false,
  canonical,
}) => {
  const router = useRouter();
  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://nhlakanipho-portfolio.vercel.app';
  const currentUrl = canonical || `${siteUrl}${router.asPath}`;
  const imageUrl = image.startsWith('http') ? image : `${siteUrl}${image}`;

  const structuredData = {
    '@context': 'https://schema.org',
    '@type': type === 'profile' ? 'Person' : 'WebSite',
    name: title,
    description,
    url: currentUrl,
    image: imageUrl,
    ...(type === 'profile' && {
      jobTitle: 'Aspiring Software Engineer | Final-Year @WeThinkCode',
      worksFor: {
        '@type': 'Organization',
        name: 'WeThinkCode_',
      },
      sameAs: [
        'https://github.com/micanipho',
        'https://linkedin.com/in/nqmasilela',
      ],
    }),
  };

  return (
    <Head>
      {/* Primary Meta Tags */}
      <title>{title}</title>
      <meta name="title" content={title} />
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords.join(', ')} />
      <meta name="author" content={author} />
      <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
      
      {/* Canonical URL */}
      <link rel="canonical" href={currentUrl} />
      
      {/* Robots */}
      {noindex && <meta name="robots" content="noindex, nofollow" />}
      
      {/* Open Graph / Facebook */}
      <meta property="og:type" content={type} />
      <meta property="og:url" content={currentUrl} />
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={imageUrl} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:site_name" content="Nhlakanipho Portfolio" />
      <meta property="og:locale" content="en_US" />

      {/* Twitter */}
      <meta property="twitter:card" content="summary_large_image" />
      <meta property="twitter:url" content={currentUrl} />
      <meta property="twitter:title" content={title} />
      <meta property="twitter:description" content={description} />
      <meta property="twitter:image" content={imageUrl} />
      <meta property="twitter:creator" content="@nhlakanipho" />
      
      {/* Article specific meta tags */}
      {type === 'article' && publishedTime && (
        <meta property="article:published_time" content={publishedTime} />
      )}
      {type === 'article' && modifiedTime && (
        <meta property="article:modified_time" content={modifiedTime} />
      )}
      {type === 'article' && (
        <meta property="article:author" content={author} />
      )}
      
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />
      
      {/* Performance and Security */}
      <meta httpEquiv="X-UA-Compatible" content="IE=edge" />
      <meta name="format-detection" content="telephone=no" />
      <meta name="theme-color" content="#15142b" />
      
      {/* Preload critical resources - commented out until custom fonts are added */}
      {/* <link
        rel="preload"
        href="/fonts/roboto-v30-latin-regular.woff2"
        as="font"
        type="font/woff2"
        crossOrigin="anonymous"
      /> */}
      
      {/* Favicon */}
      <link rel="icon" href="/favicon.ico" />
      <link rel="manifest" href="/site.webmanifest" />
      {/* Additional favicon sizes can be added when custom icons are created */}
      {/* <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" /> */}
      {/* <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" /> */}
      {/* <link rel="apple-touch-icon" href="/apple-touch-icon.png" /> */}
    </Head>
  );
};

export default SEO;
