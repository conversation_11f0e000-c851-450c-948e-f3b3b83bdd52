# Dependencies
/node_modules
/.pnp
.pnp.js
.pnp.loader.mjs

# Testing
/coverage
*.lcov

# Next.js
/.next/
/out/
.next

# Production builds
/build
/dist

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env*.local

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/
jspm_packages/

# TypeScript
*.tsbuildinfo
next-env.d.ts
*.d.ts.map

# Bundle analyzer reports
bundle-analyzer-report.html
webpack-bundle-analyzer-report.html

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# IDE and editor files
.idea
.vscode
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary folders
tmp/
temp/

# Vercel
.vercel

# Sanity
.sanity

# Lighthouse CI
.lighthouseci

# Performance monitoring
.next/trace

# Sentry
.sentryclirc

# Local Netlify folder
.netlify

# Storybook build outputs
storybook-static

# Temporary files
*.tmp
*.temp

# Lock files (uncomment if you want to ignore them)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# Certificate files
*.pem
*.key
*.crt

# Local development certificates
localhost.pem
localhost-key.pem